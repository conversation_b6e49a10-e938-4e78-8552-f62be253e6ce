# セレモニー社オンプレ移設 技術的可否と概算

## 概要

親会社の変更により、セレモニー社からFDNシステムのクラウド環境から社内ローカルネットワークへの移設要望を受けました。本資料では技術的可否と概算費用・期間について整理します。

---

## 1. 技術的可否

### ✅ オンプレ移設は技術的に可能です

#### 移設における技術的課題と対応

| 項目 | 状況 | 対応 |
|------|------|------|
| **ソース分離** | 🔄 **必要** | 現在の共有ソースからセレモニー社専用環境への分離 |
| **データベース分離** | 🔄 **必要** | 業務DB（fdndt07121270）は分離済み、システム共通DB（fdn_sys）の分離が必要 |
| **外部システム連携** | 🔄 **要対応** | 車両連携システムの接続先変更 |
| **サーバー環境** | 🔄 **要構築** | 社内ネットワーク環境での構築 |

#### ソース分離の詳細

現在のカスタマイズキー優先順位システム：
- **セレモニー社**: `ceremony_k,ceremony,ver02,ceremo,sanmen`
- **ファイル読み込み例**:
  1. `mstr.codenmmst.ceremony_k.js` を探す
  2. なければ `mstr.codenmmst.ceremony.js` を探す
  3. なければ `mstr.codenmmst.ver02.js` を探す
  4. なければ `mstr.codenmmst.ceremo.js` を探す
  5. なければ `mstr.codenmmst.sanmen.js` を探す
  6. なければ `mstr.codenmmst.js`（標準）を使用

**オンプレ環境では**：
- セレモニー社専用ファイルのみを配置
- 他社のカスタマイズファイルを除外
- 標準ファイルとの依存関係を整理

#### データベース分離の詳細

**現在の構成**：
- **システム共通DB（fdn_sys）**: 全葬儀社の会社情報、ユーザー情報等を格納
- **業務DB（fdndt07121270）**: セレモニー社の業務データ（既に分離済み）

**分離が必要な理由**：
- fdn_sysには他社の機密情報（会社情報、ユーザー情報、カスタマイズキー等）が含まれる
- オンプレ環境ではセレモニー社の情報のみを保持する必要がある

**対応方法**：
- セレモニー社専用のシステムDBを新規作成
- セレモニー社関連データのみを抽出・移行

---

## 2. 概算費用（作業内訳・人月換算）

| 作業内容 | 人月 | 金額（万円） | 備考 |
|----------|------|-------------|------|
| **ソース分離** | 1.5人月 | 135 | セレモニー社専用ソース抽出・整理 |
| **データベース分離** | 1.0人月 | 90 | システム共通DB（fdn_sys）からセレモニー社データ抽出 |
| **サーバー構築・環境設定** | 1.0人月 | 90 | 社内ネットワーク環境構築 |
| **データ移行** | 0.5人月 | 45 | 業務データ移行・検証 |
| **車両連携調整** | 0.5人月 | 45 | 車両連携システムの接続先変更 |
| **テスト・検証** | 1.0人月 | 90 | 機能テスト、性能テスト |
| **移行支援・切替** | 0.5人月 | 45 | 本番切替、ユーザー研修 |
| **保守運用準備** | 0.5人月 | 45 | 運用手順書、障害対応手順書作成 |

### **合計：6.5人月（585万円）**

※サーバー機材費・ライセンス費用は別途（セレモニー社負担想定）

---

## 3. 想定期間

### **4〜6か月**（設計・構築・移行・テスト含む）

#### 詳細スケジュール
- **要件定義・設計**: 1か月
- **ソース分離・環境構築**: 2か月  
- **システム移行・テスト**: 2〜3か月
- **本番移行・安定化**: 1か月

---

## 4. 着手可能時期

### **ライフランド案件完了後（2026年5月以降）**

- **最短稼働時期**: **2026年秋〜冬頃**を想定

---

## 5. 保守運用費用

### 定額保守（現行継続）
- **現行の定額保守**: 継続

### オンサイト保守（追加）
- **1回あたり**: 8〜12万円
- **想定頻度**: 月1〜2回程度
- **内容**: 現地でのシステム点検、障害対応、ユーザーサポート

---

## 6. 技術的留意事項

### 6.1 ソース分離の主要作業
- セレモニー社関連ファイル（ceremony_k, ceremony, ver02, ceremo, sanmen）の抽出
- 他社カスタマイズファイルの除外
- 標準ファイルとの依存関係整理
- 設定ファイルの調整

### 6.2 データベース分離の主要作業
- システム共通DB（fdn_sys）からセレモニー社関連データの抽出
- 他社の機密情報を含まない専用システムDBの構築
- 会社情報、ユーザー情報、権限設定等の移行

### 6.3 移設時の主要検討点
1. **車両連携**: 接続先の社内ネットワーク対応
2. **セキュリティ**: 社内ネットワークでのアクセス制御
3. **バックアップ**: データバックアップ体制の構築
4. **監視**: システム監視・ログ管理体制

### 6.4 リスク要因
- ソース分離時の依存関係の複雑さ
- システム共通DB分離時の他社データ混入リスク
- 車両連携システムの接続確認に時間を要する可能性
- 社内ネットワーク環境による制約
- データ移行時の業務停止時間

---

## 7. 著作権・ライセンスに関する考慮事項

### 7.1 FDNシステムの著作権
- **FDNシステムの著作権**: 当社が保有
- **オンプレ移設**: 著作権者である当社が実施するため問題なし
- **ソースコード**: セレモニー社への提供は著作権の範囲内

### 7.2 第三者ライブラリ・フレームワーク
- **Zend Framework**: オープンソース（New BSD License）
- **Smarty**: オープンソース（LGPL）
- **PostgreSQL**: オープンソース（PostgreSQL License）
- **jQuery**: オープンソース（MIT License）

### 7.3 ライセンス上の制約
- オープンソースライブラリは商用利用可能
- 特別なライセンス料は不要
- ただし、各ライブラリのライセンス条項に従う必要あり

---

## 8. 結論

**技術的には十分実現可能**です。ソース分離とシステム共通DB分離が主要な工数となりますが、既存のセレモニー社専用カスタマイズ資産を活用できるため、比較的スムーズな移行が期待できます。

著作権・ライセンス面でも特段の問題はなく、当社が著作権を保有するシステムの移設として適切に実施可能です。

---

**作成日**: 2025年1月31日
**対象**: セレモニー社オンプレ移設検討
**ステータス**: 概算見積もり（会長相談用）
